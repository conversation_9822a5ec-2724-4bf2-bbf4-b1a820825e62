#include "ams_client.h"

#include "api/api_manager.h"
#include "api/services/app_service.h"
#include "api/services/audio_service.h"
#include "api/services/chat_service.h"
#include "api/services/conversation_service.h"
#include "api/services/file_service.h"
#include "api/services/task_service.h"
#include "include/app.h"
#include "include/audio.h"

namespace amssdk {

AmsClient::AmsClient(const std::string& base_url)
    : api_manager_(std::make_unique<ApiManager>(base_url)) {}

AmsClient::~AmsClient() = default;

bool AmsClient::SetAuthorizationKey(const std::string& key) const {
  return api_manager_->SetAuthorizationKey(key);
}

void AmsClient::SetMaxTimeout(int32_t ms) const {
  api_manager_->SetMaxTimeout(ms);
}

ApiResult<void> AmsClient::SendChatMessage(
    const ChatRequest& request, const StreamEventCallback& callback) const {
  return api_manager_->chat().SendChatMessage(request, callback);
}
ApiResult<void> AmsClient::SendCompletionMessage(
    const CompletionMessageRequest& request,
    const StreamEventCallback& callback) const {
  return api_manager_->chat().SendCompletionMessage(request, callback);
}

ApiResult<FileResponse> AmsClient::FileUpload(
    const FileRequest& request) const {
  return api_manager_->file().Upload(request.GetFilePath(), request.GetUser());
}
ApiResult<SimpleResponse> AmsClient::StopTask(
    const TaskStopRequest& request) const {
  return api_manager_->task().StopTask(request);
}
ApiResult<SimpleResponse> AmsClient::SendFeedback(
    const FeedbackRequest& request) const {
  return api_manager_->task().SendFeedback(request);
}
ApiResult<SuggestedResponse> AmsClient::GetSuggested(
    const SuggestedRequest& request) const {
  return api_manager_->conversation().GetSuggested(request);
}
ApiResult<MessagesResponse> AmsClient::GetMessages(
    const MessagesRequest& request) const {
  return api_manager_->conversation().GetMessages(request);
}
ApiResult<ConversationResponse> AmsClient::GetConversation(
    const ConversationRequest& request) const {
  return api_manager_->conversation().GetConversation(request);
}
ApiResult<SimpleResponse> AmsClient::DeleteConversation(
    const DeleteConversationRequest& request) const {
  return api_manager_->conversation().DeleteConversation(request);
}
ApiResult<RenameConversationResponse> AmsClient::RenameConversation(
    const RenameConversationRequest& request) const {
  return api_manager_->conversation().RenameConversation(request);
}
ApiResult<AudioToTextResponse> AmsClient::AudioToText(
    const AudioToTextRequest& request) const {
  return api_manager_->audio().AudioToText(request);
}
ApiResult<AppMetaResponse> AmsClient::AppMeta(
    const AppMetaRequest& request) const {
  return api_manager_->app().Meta(request);
}
ApiResult<AppInfoResponse> AmsClient::AppInfo(
    const AppInfoRequest& request) const {
  return api_manager_->app().Info(request);
}
ApiResult<AppParamResponse> AmsClient::AppParameters(
    const AppParamRequest& request) const {
  return api_manager_->app().Parameters(request);
}

}  // namespace amssdk
