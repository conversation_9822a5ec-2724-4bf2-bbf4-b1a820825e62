#include "response.h"

amssdk::Response::Response(const amssdk::Response& other) noexcept
    : status_code(other.status_code),
      elapsed(other.elapsed),
      status_line(other.status_line),
      content(other.content),
      url(other.url),
      reason(other.reason),
      raw_json(other.raw_json) {}

amssdk::Response::Response(amssdk::Response&& other) noexcept
    : status_code(other.status_code),
      elapsed(other.elapsed),
      status_line(std::move(other.status_line)),
      content(std::move(other.content)),
      url(std::move(other.url)),
      reason(std::move(other.reason)),
      raw_json(std::move(other.raw_json)) {}

amssdk::Response::Response(std::string&& url, std::string&& content,
                           std::string&& status_line, std::string&& reason,
                           long status_code, double elapsed) noexcept(false)
    : status_code(status_code),
      elapsed(elapsed),
      status_line(std::move(status_line)),
      content(std::move(content)),
      url(url),
      reason(std::move(reason)) {
  try {
    if (!this->content.empty()) {
      if (this->content[0] == '{') {
        this->raw_json = nlohmann::json::parse(this->content);
      } else {
        this->raw_json = nlohmann::json();
      }
    } else {
      this->raw_json = nlohmann::json();
    }
  } catch (nlohmann::json::parse_error& e) {
    throw amssdk::exception::OpenAIException(
        e.what(), amssdk::exception::EType::E_FAILURETOPARSE,
        "amssdkResponse::Response(std::string&&, std::string&&, ...)");
  }

  // check the response for errors -- nothrow on success
  this->CheckResponse();
}

amssdk::Response& amssdk::Response::operator=(
    const amssdk::Response& other) noexcept {
  this->status_code = other.status_code;
  this->elapsed = other.elapsed;
  this->status_line = other.status_line;
  this->content = other.content;
  this->url = other.url;
  this->reason = other.reason;
  this->raw_json = other.raw_json;

  return *this;
}

amssdk::Response& amssdk::Response::operator=(
    amssdk::Response&& other) noexcept {
  this->status_code = other.status_code;
  this->elapsed = other.elapsed;
  this->status_line = std::move(other.status_line);
  this->content = std::move(other.content);
  this->url = std::move(other.url);
  this->reason = std::move(other.reason);
  this->raw_json = std::move(other.raw_json);

  return *this;
}

namespace amssdk {

std::ostream& operator<<(std::ostream& os, const Response& r) {
  !r.raw_json.empty() ? os << r.raw_json.dump(4) : os << "null";
  return os;
}

}  // namespace liboai

void amssdk::Response::CheckResponse() const noexcept(false) {
  if (this->status_code == 429) {
    throw amssdk::exception::OpenAIRateLimited(
        !this->reason.empty() ? this->reason : "Rate limited",
        amssdk::exception::EType::E_RATELIMIT,
        "amssdkResponse::CheckResponse()");
  } else if (this->status_code == 0) {
    throw amssdk::exception::OpenAIException(
        "A connection error occurred",
        amssdk::exception::EType::E_CONNECTIONERROR,
        "amssdkResponse::CheckResponse()");
  } else if (this->status_code < 200 || this->status_code >= 300) {
    if (this->raw_json.contains("error")) {
      try {
        throw amssdk::exception::OpenAIException(
            this->raw_json["error"]["message"].get<std::string>(),
            amssdk::exception::EType::E_APIERROR,
            "amssdkResponse::CheckResponse()");
      } catch (nlohmann::json::parse_error& e) {
        throw amssdk::exception::OpenAIException(
            e.what(), amssdk::exception::EType::E_FAILURETOPARSE,
            "amssdkResponse::CheckResponse()");
      }
    } else {
      throw amssdk::exception::OpenAIException(
          !this->reason.empty() ? this->reason : "An unknown error occurred",
          amssdk::exception::EType::E_BADREQUEST,
          "amssdkResponse::CheckResponse()");
    }
  }
}
