#ifndef AMSSDK_API_RESULT_H
#define AMSSDK_API_RESULT_H

#include <string>
#include "network/response.h"

namespace amssdk {
class Response;

struct SdkError {
  int status_code = 0;
  std::string code;
  std::string message;
  std::string reason;
};

template <typename T>
struct ApiResult {
  bool ok = false;
  bool success = false;
  int status_code = 0;
  double elapsed_ms = 0.0;
  std::string request_url;
  std::string raw_body;

  T data{};
  SdkError error{};
};

template <>
struct ApiResult<void> {
  bool ok = false;
  bool success = false;
  int status_code = 0;
  double elapsed_ms = 0.0;
  std::string request_url;
  std::string raw_body;
  SdkError error{};
};

template <typename T>
ApiResult<T> BuildResult(const Response& response) {
  ApiResult<T> result;
  result.status_code = response.status_code;
  result.elapsed_ms = response.elapsed;
  result.request_url = response.url;
  result.raw_body = response.raw_json.dump();
  result.ok = (response.status_code >= 200 && response.status_code < 300);

  if (result.ok && !response.raw_json.empty()) {
    nlohmann::json j = response.raw_json;
    result.success = j.value("result", true);
  }

  if (!result.ok) {
    result.error.status_code = response.status_code;
    result.error.reason = response.reason;
    result.error.message = response.status_line;
  }
  return result;
}
}  // namespace amssdk

#endif  // AMSSDK_API_RESULT_H
