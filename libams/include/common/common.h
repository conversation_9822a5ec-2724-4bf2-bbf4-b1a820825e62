#ifndef AMSSDK_COMMON_H
#define AMSSDK_COMMON_H

#include <string>
#include <vector>

namespace amssdk {

struct FileAttachment {
  enum class FileType { IMAGE, VIDEO, AUDIO, DOCUMENT };
  enum class TransferMethod { LOCAL_FILE, URL };
  FileType type;
  std::string url;
  std::string upload_file_id;
  size_t size;
  TransferMethod transfer_method;
};

struct Usage {
  double latency;
  int total_tokens;
  int prompt_tokens;
  int completion_tokens;
  std::string currency;
  std::string total_price;
  std::string prompt_unit_price;
  std::string prompt_price_unit;
  std::string completion_unit_price;
  std::string completion_price_unit;
};

struct RetrieverResource {
  int position;
  double score;
  std::string dataset_id;
  std::string dataset_name;
  std::string document_id;
  std::string document_name;
  std::string segment_id;
  std::string content;
};

struct MetaData {
  Usage usage;
  std::vector<RetrieverResource> retriever_resources;
};

class SimpleResponse {
 public:
  enum class ResultType { kSuccess, kFailure };
  ResultType result = ResultType::kSuccess;
};

}  // namespace amssdk

#endif  //AMSSDK_COMMON_H
