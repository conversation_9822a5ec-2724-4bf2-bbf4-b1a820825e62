#include "api_manager.h"

#include "services/app_service.h"
#include "services/audio_service.h"
#include "services/chat_service.h"
#include "services/conversation_service.h"
#include "services/file_service.h"
#include "services/task_service.h"

namespace amssdk {

ApiManager::ApiManager(const std::string& base_url)
    : base_url_(base_url), auth_(Authorization::Authorizer()) {
  if (!base_url.empty()) {
    http_client_.SetBaseUrl(base_url);
  }
  InitializeServices();
}

ApiManager::~ApiManager() = default;

ChatService& ApiManager::chat() {
  return *chat_service_;
}

bool ApiManager::SetAuthorizationKey(const std::string& key) {
  return auth_.SetKey(key);
}

void ApiManager::SetMaxTimeout(int32_t ms) {
  auth_.SetMaxTimeout(ms);
}

void ApiManager::InitializeServices() {
  chat_service_ = std::make_unique<ChatService>(*this);
  task_service_ = std::make_unique<TaskService>(*this);
  file_service_ = std::make_unique<FileService>(*this);
  audio_service_ = std::make_unique<AudioService>(*this);
  app_service_ = std::make_unique<AppService>(*this);
  conversation_service_ = std::make_unique<ConversationService>(*this);
}

FileService& ApiManager::file() const {
  return *file_service_;
}
AudioService& ApiManager::audio() const {
  return *audio_service_;
}
TaskService& ApiManager::task() const {
  return *task_service_;
}
ConversationService& ApiManager::conversation() const {
  return *conversation_service_;
}
AppService& ApiManager::app() const {
  return *app_service_;
}

void ApiManager::SetBaseUrl(const std::string& base_url) {
  base_url_ = base_url;
  http_client_.SetBaseUrl(base_url);
}

}  // namespace amssdk